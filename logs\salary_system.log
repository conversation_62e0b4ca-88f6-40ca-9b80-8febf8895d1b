2025-07-16 21:33:16.111 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-16 21:33:16.111 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-16 21:33:16.111 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-16 21:33:16.111 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-16 21:33:16.111 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-07-16 21:33:16.111 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-16 21:33:19.564 | INFO     | __main__:setup_app_logging:231 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-16 21:33:19.564 | INFO     | __main__:main:295 | 初始化核心管理器...
2025-07-16 21:33:19.565 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 21:33:19.565 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 21:33:19.567 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-16 21:33:19.569 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-07-16 21:33:19.571 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-16 21:33:19.615 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-16 21:33:19.615 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-16 21:33:19.620 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 21:33:19.620 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-16 21:33:19.621 | INFO     | __main__:main:300 | 核心管理器初始化完成。
2025-07-16 21:33:19.624 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-16 21:33:19.625 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-16 21:33:19.688 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-07-16 21:33:19.688 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-07-16 21:33:19.799 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:130 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-16 21:33:19.800 | INFO     | src.modules.data_import.config_sync_manager:__init__:71 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-07-16 21:33:19.805 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-16 21:33:19.807 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-16 21:33:19.808 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-16 21:33:19.808 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-16 21:33:19.809 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-16 21:33:19.809 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-16 21:33:19.811 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-16 21:33:19.812 | INFO     | src.services.table_data_service:__init__:68 | 表格数据服务初始化完成
2025-07-16 21:33:19.813 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 121.4ms
2025-07-16 21:33:19.842 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-07-16 21:33:19.843 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-07-16 21:33:19.850 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-07-16 21:33:19.851 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-07-16 21:33:19.852 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-07-16 21:33:19.854 | INFO     | src.gui.prototype.prototype_main_window:__init__:2879 | 🚀 性能管理器已集成
2025-07-16 21:33:19.854 | INFO     | src.gui.prototype.prototype_main_window:__init__:2881 | ✅ 新架构集成成功！
2025-07-16 21:33:19.856 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:2992 | 🔧 [修复标识] ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-16 21:33:19.856 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:2958 | ✅ 新架构事件监听器设置完成
2025-07-16 21:33:19.857 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-16 21:33:19.867 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-07-16 21:33:19.868 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-16 21:33:20.521 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2161 | 菜单栏创建完成
2025-07-16 21:33:20.522 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-16 21:33:20.525 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-16 21:33:20.526 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-16 21:33:20.530 | INFO     | src.gui.prototype.prototype_main_window:__init__:2137 | 菜单栏管理器初始化完成
2025-07-16 21:33:20.531 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-07-16 21:33:20.532 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:3852 | 管理器设置完成，包含增强版表头管理器
2025-07-16 21:33:20.536 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-16 21:33:20.541 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-16 21:33:20.547 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-07-16 21:33:20.554 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-16 21:33:20.556 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-16 21:33:20.557 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-07-16 21:33:20.559 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-16 21:33:20.560 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-07-16 21:33:20.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1312 | 快捷键注册完成: 18/18 个
2025-07-16 21:33:20.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1523 | 拖拽排序管理器初始化完成
2025-07-16 21:33:20.597 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-07-16 21:33:20.616 | INFO     | src.gui.multi_column_sort_manager:__init__:103 | 多列排序管理器初始化完成，最大排序列数: 3
2025-07-16 21:33:20.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1917 | 多列排序管理器初始化完成
2025-07-16 21:33:20.620 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1934 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-16 21:33:20.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3614 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-16 21:33:20.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-16 21:33:20.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2161 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 5.77ms
2025-07-16 21:33:20.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 初始化和状态: 1.61ms (27.8%)
2025-07-16 21:33:20.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 21:33:20.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 数据模型更新: 1.06ms (18.4%)
2025-07-16 21:33:20.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 表头设置: 1.00ms (17.3%)
2025-07-16 21:33:20.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 21:33:20.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-16 21:33:20.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 21:33:20.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行号: 2.11ms (36.5%)
2025-07-16 21:33:20.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2169 | 🔧 [性能分析] 最耗时步骤: 设置行号 (2.11ms)
2025-07-16 21:33:20.650 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1736 | 表格已初始化为空白状态，等待用户导入数据
2025-07-16 21:33:20.655 | INFO     | src.gui.widgets.pagination_widget:__init__:165 | 分页组件初始化完成
2025-07-16 21:33:20.683 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:533 | 控制面板按钮信号连接完成
2025-07-16 21:33:20.697 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 21:33:20.697 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-16 21:33:20.699 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:3825 | 快捷键设置完成
2025-07-16 21:33:20.701 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:3814 | 主窗口UI设置完成。
2025-07-16 21:33:20.707 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3863 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-16 21:33:20.712 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3891 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-07-16 21:33:20.714 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3900 | ✅ 已连接分页组件事件到新架构
2025-07-16 21:33:20.714 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3902 | 信号连接设置完成
2025-07-16 21:33:20.715 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:4627 | 已加载字段映射信息，共0个表的映射
2025-07-16 21:33:20.723 | WARNING  | src.gui.prototype.prototype_main_window:set_data:623 | 尝试设置空数据，将显示提示信息。
2025-07-16 21:33:20.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3614 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-16 21:33:20.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-16 21:33:20.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2161 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 16.73ms
2025-07-16 21:33:20.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 初始化和状态: 6.79ms (40.6%)
2025-07-16 21:33:20.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 字段映射: 1.04ms (6.2%)
2025-07-16 21:33:20.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 数据模型更新: 5.22ms (31.2%)
2025-07-16 21:33:20.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 表头设置: 2.63ms (15.7%)
2025-07-16 21:33:20.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 21:33:20.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 填充可见数据: 1.06ms (6.4%)
2025-07-16 21:33:20.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 21:33:20.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-16 21:33:20.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2169 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (6.79ms)
2025-07-16 21:33:20.802 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1736 | 表格已初始化为空白状态，等待用户导入数据
2025-07-16 21:33:20.807 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-16 21:33:20.817 | WARNING  | src.gui.prototype.prototype_main_window:set_data:623 | 尝试设置空数据，将显示提示信息。
2025-07-16 21:33:20.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3614 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-16 21:33:20.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-16 21:33:20.825 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2161 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 6.23ms
2025-07-16 21:33:20.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 初始化和状态: 4.15ms (66.6%)
2025-07-16 21:33:20.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 21:33:20.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 数据模型更新: 1.04ms (16.8%)
2025-07-16 21:33:20.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 表头设置: 1.04ms (16.7%)
2025-07-16 21:33:20.848 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 21:33:20.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-16 21:33:20.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 21:33:20.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-16 21:33:20.875 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2169 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (4.15ms)
2025-07-16 21:33:20.888 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1736 | 表格已初始化为空白状态，等待用户导入数据
2025-07-16 21:33:20.890 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-16 21:33:20.894 | INFO     | src.gui.prototype.prototype_main_window:_setup_legacy_compatibility:6500 | ✅ 老架构兼容性接口设置完成
2025-07-16 21:33:20.908 | INFO     | src.gui.prototype.prototype_main_window:__init__:2932 | 原型主窗口初始化完成
2025-07-16 21:33:21.194 | INFO     | __main__:main:322 | 应用程序启动成功
2025-07-16 21:33:21.202 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-07-16 21:33:21.210 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-07-16 21:33:21.215 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-07-16 21:33:21.216 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1884 | MainWorkspaceArea 响应式适配: sm
2025-07-16 21:33:21.357 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-07-16 21:33:21.416 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-16 21:33:21.416 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-16 21:33:21.439 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-16 21:33:21.439 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:968 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-16 21:33:21.439 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:880 | 找到 3 个总表
2025-07-16 21:33:21.439 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1290 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-16 21:33:21.439 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1187 | 使用兜底数据加载导航
2025-07-16 21:33:21.639 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-07-16 21:33:21.639 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1028 | 开始获取最新工资数据路径...
2025-07-16 21:33:21.641 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-16 21:33:21.641 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1033 | 未找到任何工资数据表
2025-07-16 21:33:21.641 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1098 | 未找到最新工资数据路径
2025-07-16 21:33:26.705 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:550 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-16 21:33:26.707 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4134 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-16 21:33:26.714 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-16 21:33:26.715 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-07-16 21:33:26.718 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-16 21:33:26.750 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-16 21:33:26.759 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-16 21:33:26.804 | WARNING  | src.gui.main_dialogs:_get_template_fields:1872 | 🔧 [修复] ConfigSyncManager未设置，无法获取模板字段
2025-07-16 21:33:26.805 | WARNING  | src.gui.main_dialogs:_init_field_mapping:1859 | 未找到字段模板，使用默认字段列表
2025-07-16 21:33:26.861 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-16 21:33:26.864 | INFO     | src.gui.main_dialogs:_apply_default_settings:2210 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-16 21:33:26.869 | INFO     | src.gui.main_dialogs:_setup_tooltips:2465 | 工具提示设置完成
2025-07-16 21:33:26.870 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2504 | 快捷键设置完成
2025-07-16 21:33:26.871 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-16 21:33:26.872 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-07-16 21:33:26.873 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4145 | 🔧 [修复] ConfigSyncManager已设置到数据导入对话框
2025-07-16 21:33:33.426 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 21:33:34.952 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 21:33:34.953 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-16 21:33:34.957 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2245 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-16 21:33:42.920 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 21:33:43.118 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 21:33:43.135 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 21:33:43.136 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 21:33:43.138 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 21:33:43.354 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 21:33:43.355 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 21:33:43.359 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 21:33:43.359 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 21:33:43.360 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 21:33:43.483 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-16 21:33:43.488 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 21:33:43.491 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-16 21:33:43.492 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-16 21:33:43.507 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 21:33:43.508 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-07-16 21:33:43.510 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-16 21:33:43.514 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-16 21:33:43.514 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-07-16 21:33:43.515 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-16 21:33:43.516 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-16 21:33:43.518 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-16 21:33:43.527 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:606 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-16 21:33:43.535 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-16 21:33:43.543 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-16 21:33:43.546 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-16 21:33:43.553 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-16 21:33:43.561 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-16 21:33:43.640 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1127 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-16 21:33:43.652 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-16 21:33:43.671 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1144 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-16 21:33:43.684 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1281 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-16 21:33:43.686 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 21:33:43.686 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 21:33:43.687 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 21:33:43.811 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-16 21:33:43.816 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 21:33:43.818 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-16 21:33:43.819 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-16 21:33:43.822 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 21:33:43.823 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-07-16 21:33:43.829 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-16 21:33:43.832 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-16 21:33:43.834 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-07-16 21:33:43.834 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-16 21:33:43.835 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-16 21:33:43.836 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-16 21:33:43.840 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:606 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-16 21:33:43.842 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-16 21:33:43.847 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-16 21:33:43.847 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-16 21:33:43.853 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-16 21:33:43.864 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-16 21:33:43.878 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1127 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-16 21:33:43.879 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-16 21:33:43.892 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1144 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-16 21:33:43.906 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1281 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-16 21:33:43.907 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 21:33:43.908 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 21:33:43.908 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 21:33:44.049 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-16 21:33:44.052 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 21:33:44.055 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-16 21:33:44.056 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-16 21:33:44.062 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 21:33:44.069 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-07-16 21:33:44.074 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-16 21:33:44.075 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-16 21:33:44.077 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-07-16 21:33:44.078 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-16 21:33:44.087 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-16 21:33:44.087 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-16 21:33:44.092 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:606 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-16 21:33:44.092 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-16 21:33:44.105 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-16 21:33:44.107 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-16 21:33:44.113 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_active_employees
2025-07-16 21:33:44.120 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-16 21:33:44.144 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1127 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-16 21:33:44.146 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-16 21:33:44.147 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1144 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-16 21:33:44.190 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1281 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-16 21:33:44.192 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 21:33:44.195 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 21:33:44.196 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 21:33:44.315 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-16 21:33:44.318 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 21:33:44.322 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-16 21:33:44.323 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-16 21:33:44.326 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 21:33:44.332 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-07-16 21:33:44.335 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-16 21:33:44.338 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-16 21:33:44.339 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-07-16 21:33:44.340 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-16 21:33:44.348 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-16 21:33:44.349 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-16 21:33:44.359 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:606 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-16 21:33:44.381 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-16 21:33:44.390 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-07-16 21:33:44.414 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-16 21:33:44.418 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:692 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-16 21:33:44.430 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-16 21:33:44.451 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1127 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-16 21:33:44.452 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-16 21:33:44.473 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1144 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-16 21:33:44.488 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1281 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-16 21:33:44.495 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-16 21:33:44.500 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-16 21:33:44.507 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4155 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-16 21:33:44.509 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4166 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-16 21:33:44.509 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4184 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-16 21:33:45.310 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4272 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-16 21:33:45.310 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4276 | 检测到工资数据导入，开始刷新导航面板
2025-07-16 21:33:45.314 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4280 | 使用强制刷新方法
2025-07-16 21:33:45.315 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-16 21:33:45.316 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-16 21:33:45.326 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-16 21:33:45.327 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 1 个月份
2025-07-16 21:33:45.328 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-16 21:33:45.329 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-16 21:33:45.330 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-07-16 21:33:45.331 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4285 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-16 21:33:46.827 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4361 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-16 21:33:46.827 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4366 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-16 21:33:47.337 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5684 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-16 21:33:47.338 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4445 | 开始刷新当前数据显示: salary_data_2025_07_active_employees
2025-07-16 21:33:47.341 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4452 | 分页模式刷新: 第1页，每页50条
2025-07-16 21:33:47.341 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5239 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-16 21:33:47.342 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5328 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-16 21:33:47.348 | INFO     | src.gui.prototype.prototype_main_window:run:129 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-16 21:33:47.350 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-16 21:33:47.355 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-16 21:33:47.360 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-16 21:33:47.361 | INFO     | src.gui.prototype.prototype_main_window:run:155 | 🔧 [排序修复] 使用排序查询: 0 个排序列
2025-07-16 21:33:47.361 | INFO     | src.gui.prototype.prototype_main_window:run:186 | 原始数据: 50行, 28列
2025-07-16 21:33:47.363 | INFO     | src.gui.prototype.prototype_main_window:run:193 | 开始应用字段映射
2025-07-16 21:33:47.368 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4770 | 🔧 [修复标识] 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-16 21:33:47.420 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:4965 | 🔧 [修复标识] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-16 21:33:47.422 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4815 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-16 21:33:47.429 | INFO     | src.gui.prototype.prototype_main_window:run:203 | 🔧 [修复标识] PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-16 21:33:47.430 | INFO     | src.gui.prototype.prototype_main_window:run:217 | 字段映射成功: 24列
2025-07-16 21:33:47.463 | INFO     | src.gui.prototype.prototype_main_window:run:227 | 开始应用数据格式化处理
2025-07-16 21:33:47.464 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-16 21:33:47.481 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-16 21:33:47.484 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-16 21:33:47.485 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-16 21:33:47.486 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-16 21:33:47.487 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-16 21:33:47.488 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-16 21:33:47.489 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '17.0']
2025-07-16 21:33:47.490 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-16 21:33:47.491 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-16 21:33:47.498 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-16 21:33:47.499 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-16 21:33:47.500 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-16 21:33:47.501 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-16 21:33:47.502 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-16 21:33:47.503 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '17']
2025-07-16 21:33:47.507 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-16 21:33:47.510 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-16 21:33:47.511 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-16 21:33:47.512 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-16 21:33:47.513 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-16 21:33:47.514 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-16 21:33:47.515 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-16 21:33:47.516 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-16 21:33:47.517 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-16 21:33:47.517 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-16 21:33:47.518 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-16 21:33:47.519 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-16 21:33:47.527 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-16 21:33:47.529 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-16 21:33:47.531 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-16 21:33:47.534 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-16 21:33:47.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-16 21:33:47.541 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-16 21:33:47.544 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-16 21:33:47.545 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-16 21:33:47.546 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-16 21:33:47.549 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-16 21:33:47.552 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-16 21:33:47.553 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-16 21:33:47.557 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-16 21:33:47.557 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-16 21:33:47.582 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-16 21:33:47.591 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-16 21:33:47.623 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-16 21:33:47.628 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-16 21:33:47.629 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-16 21:33:47.630 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-16 21:33:47.631 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-16 21:33:47.632 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-16 21:33:47.633 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-16 21:33:47.633 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-16 21:33:47.635 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-16 21:33:47.636 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-16 21:33:47.642 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-16 21:33:47.643 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-16 21:33:47.644 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-16 21:33:47.645 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-16 21:33:47.645 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-16 21:33:47.646 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-16 21:33:47.647 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-16 21:33:47.648 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-16 21:33:47.654 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-16 21:33:47.659 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-16 21:33:47.660 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-16 21:33:47.662 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-16 21:33:47.663 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-16 21:33:47.663 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:47.666 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:47.666 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-16 21:33:47.667 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-16 21:33:47.668 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-16 21:33:47.676 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-16 21:33:47.676 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-16 21:33:47.678 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-16 21:33:47.678 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-16 21:33:47.679 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:47.683 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:47.694 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-16 21:33:47.695 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-16 21:33:47.697 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-16 21:33:47.698 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-16 21:33:47.699 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-16 21:33:47.701 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-16 21:33:47.701 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-16 21:33:47.702 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-16 21:33:47.710 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:47.712 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-16 21:33:47.729 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-16 21:33:47.735 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-16 21:33:47.735 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-16 21:33:47.736 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-16 21:33:47.738 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-16 21:33:47.739 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-16 21:33:47.740 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-16 21:33:47.743 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-16 21:33:47.767 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-16 21:33:47.768 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-16 21:33:47.771 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:47.774 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-16 21:33:47.777 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-16 21:33:47.783 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:47.784 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-16 21:33:47.785 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-16 21:33:47.786 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-16 21:33:47.787 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-16 21:33:47.788 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-16 21:33:47.789 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-16 21:33:47.797 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-16 21:33:47.798 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-16 21:33:47.800 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-16 21:33:47.801 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-16 21:33:47.803 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-16 21:33:47.819 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-16 21:33:47.837 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-16 21:33:47.850 | INFO     | src.gui.prototype.prototype_main_window:run:235 | 数据格式化成功: 50行, 24列
2025-07-16 21:33:47.852 | INFO     | src.gui.prototype.prototype_main_window:run:259 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-16 21:33:47.853 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5359 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-16 21:33:47.855 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5393 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_07_active_employees 第1页
2025-07-16 21:33:47.865 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-16 21:33:47.867 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5424 | 🔧 [异步分页] 开始应用数据格式化处理
2025-07-16 21:33:47.868 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: salary_data_2025_07_active_employees
2025-07-16 21:33:47.870 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-16 21:33:47.873 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-16 21:33:47.875 | INFO     | src.modules.data_storage.salary_data_formatter:_extract_table_type_from_name:335 | 🔧 [表名映射] 通过关键词 'active_employees' 匹配到表类型: active_employees
2025-07-16 21:33:47.885 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: salary_data_2025_07_active_employees, 提取的表类型: active_employees
2025-07-16 21:33:47.889 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-16 21:33:47.890 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-16 21:33:47.891 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-16 21:33:47.891 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['01', '01', '17']
2025-07-16 21:33:47.892 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_id 不存在于数据框中
2025-07-16 21:33:47.894 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_id_fields:461 | 🔧 [警告] ID字段 employee_type_code 不存在于数据框中
2025-07-16 21:33:47.898 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-16 21:33:47.903 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-16 21:33:47.904 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-16 21:33:47.904 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-16 21:33:47.905 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-16 21:33:47.906 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-16 21:33:47.909 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-16 21:33:47.910 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-16 21:33:47.910 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-16 21:33:47.914 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-16 21:33:47.916 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-16 21:33:47.917 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_id 不存在于数据框中
2025-07-16 21:33:47.918 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_name 不存在于数据框中
2025-07-16 21:33:47.919 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 department 不存在于数据框中
2025-07-16 21:33:47.920 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type_code 不存在于数据框中
2025-07-16 21:33:47.920 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_string_fields:593 | 🔧 [警告] 字符串字段 employee_type 不存在于数据框中
2025-07-16 21:33:47.922 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-16 21:33:47.923 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-16 21:33:47.926 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-16 21:33:47.934 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-16 21:33:47.935 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-16 21:33:47.938 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-16 21:33:47.939 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-16 21:33:47.940 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-16 21:33:47.942 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-16 21:33:47.946 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-16 21:33:47.947 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-16 21:33:47.951 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-16 21:33:47.952 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-16 21:33:47.953 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-16 21:33:47.956 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-16 21:33:47.990 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-16 21:33:47.991 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-16 21:33:47.991 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-16 21:33:47.992 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 position_salary_2025 不存在于数据框中
2025-07-16 21:33:47.993 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 grade_salary_2025 不存在于数据框中
2025-07-16 21:33:47.993 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 allowance 不存在于数据框中
2025-07-16 21:33:47.994 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 balance_allowance 不存在于数据框中
2025-07-16 21:33:47.995 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 basic_performance_2025 不存在于数据框中
2025-07-16 21:33:47.996 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 health_fee 不存在于数据框中
2025-07-16 21:33:47.997 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 transport_allowance 不存在于数据框中
2025-07-16 21:33:48.004 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 property_allowance 不存在于数据框中
2025-07-16 21:33:48.004 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 communication_allowance 不存在于数据框中
2025-07-16 21:33:48.006 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 performance_bonus_2025 不存在于数据框中
2025-07-16 21:33:48.006 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 provident_fund_2025 不存在于数据框中
2025-07-16 21:33:48.007 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 housing_allowance 不存在于数据框中
2025-07-16 21:33:48.008 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 car_allowance 不存在于数据框中
2025-07-16 21:33:48.009 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 supplement 不存在于数据框中
2025-07-16 21:33:48.010 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 advance 不存在于数据框中
2025-07-16 21:33:48.011 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 total_salary 不存在于数据框中
2025-07-16 21:33:48.012 | WARNING  | src.modules.data_storage.salary_data_formatter:_format_float_fields:697 | 🔧 [警告] 浮点型字段 pension_insurance 不存在于数据框中
2025-07-16 21:33:48.021 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-16 21:33:48.022 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-16 21:33:48.023 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-16 21:33:48.024 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-16 21:33:48.025 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-16 21:33:48.026 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-16 21:33:48.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:48.028 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:48.029 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-16 21:33:48.035 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-16 21:33:48.036 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-16 21:33:48.038 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-16 21:33:48.038 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-16 21:33:48.040 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-16 21:33:48.040 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-16 21:33:48.041 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:48.042 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:48.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-16 21:33:48.053 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-16 21:33:48.054 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-16 21:33:48.055 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-16 21:33:48.056 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-16 21:33:48.057 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-16 21:33:48.057 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-16 21:33:48.058 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:48.060 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:48.061 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-16 21:33:48.066 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-16 21:33:48.068 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-16 21:33:48.069 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-16 21:33:48.070 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-16 21:33:48.072 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-16 21:33:48.072 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-16 21:33:48.073 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.97, 189.0, 174.16], 原始类型=float64
2025-07-16 21:33:48.074 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-16 21:33:48.075 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-16 21:33:48.122 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:48.123 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:48.124 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-16 21:33:48.125 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-16 21:33:48.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-16 21:33:48.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-16 21:33:48.128 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[0.0, 0.0, 2000.0], 原始类型=float64
2025-07-16 21:33:48.136 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-16 21:33:48.140 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-16 21:33:48.156 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.97, 9897.0, 6240.16], 原始类型=float64
2025-07-16 21:33:48.162 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-16 21:33:48.162 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-16 21:33:48.163 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8, 1140.53, 1113.75], 原始类型=float64
2025-07-16 21:33:48.165 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-16 21:33:48.166 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-16 21:33:48.166 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-16 21:33:48.194 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-16 21:33:48.199 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-16 21:33:48.200 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5426 | 🔧 [异步分页] 数据格式化成功: 50行, 24列
2025-07-16 21:33:48.201 | INFO     | src.gui.prototype.prototype_main_window:set_data:663 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-16 21:33:48.208 | WARNING  | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1168 | 架构工厂不可用，跳过字段映射
2025-07-16 21:33:48.212 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1262 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-16 21:33:48.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3585 | 最大可见行数已更新: 1000 -> 50
2025-07-16 21:33:48.239 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3638 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-16 21:33:48.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19990089
2025-07-16 21:33:48.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20161565
2025-07-16 21:33:48.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20191782
2025-07-16 21:33:48.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20151515
2025-07-16 21:33:48.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20181640
2025-07-16 21:33:48.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-16 21:33:48.249 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-16 21:33:48.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2388 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-16 21:33:48.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2395 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-16 21:33:48.266 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2388 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-16 21:33:48.266 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2395 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-16 21:33:48.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2161 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 78.85ms
2025-07-16 21:33:48.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 初始化和状态: 23.34ms (29.6%)
2025-07-16 21:33:48.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 21:33:48.315 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 数据模型更新: 6.78ms (8.6%)
2025-07-16 21:33:48.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 表头设置: 4.15ms (5.3%)
2025-07-16 21:33:48.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 21:33:48.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 填充可见数据: 43.54ms (55.2%)
2025-07-16 21:33:48.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 21:33:48.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行号: 1.04ms (1.3%)
2025-07-16 21:33:48.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2169 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (43.54ms)
2025-07-16 21:33:48.321 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-16 21:33:48.321 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6018 | 🔧 [新架构排序] 表头点击: 列6
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6024 | 🔧 [排序循环] 列6: ascending → descending
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_column_sort_state:6108 | 🔧 [排序循环] 设置列6排序: descending
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3555 | 🆕 [新架构排序] 处理排序应用: 列6, 2025年薪级工资, descending
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3569 | 🔧 [排序修复] 当前页码: 1
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3349 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.prototype_main_window:_get_table_field_mapping:3485 | 🔧 [字段映射] 加载映射成功: salary_data_2025_07_active_employees, 28个字段
2025-07-16 21:34:02.691 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3370 | 🔧 [排序] 字段转换: 2025年薪级工资 -> grade_salary_2025
2025-07-16 21:34:02.707 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3389 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6018 | 🔧 [新架构排序] 表头点击: 列6
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6024 | 🔧 [排序循环] 列6: descending → none
2025-07-16 21:34:02.707 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_column_sort_state:6099 | 🔧 [排序循环] 清除列6的排序
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:3585 | 🆕 [新架构排序] 处理排序清除
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:3592 | 🔧 [排序修复] 清除排序，当前页码: 1
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3349 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-16 21:34:02.707 | WARNING  | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3376 | 🔧 [排序] 没有有效的排序列，跳过发布事件
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6018 | 🔧 [新架构排序] 表头点击: 列6
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6024 | 🔧 [排序循环] 列6: none → ascending
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-16 21:34:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_column_sort_state:6108 | 🔧 [排序循环] 设置列6排序: ascending
2025-07-16 21:34:02.707 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-16 21:34:02.723 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3555 | 🆕 [新架构排序] 处理排序应用: 列6, 2025年薪级工资, ascending
2025-07-16 21:34:02.723 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 0
2025-07-16 21:34:02.723 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3569 | 🔧 [排序修复] 当前页码: 1
2025-07-16 21:34:02.723 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3349 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-07-16 21:34:02.723 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3370 | 🔧 [排序] 字段转换: 2025年薪级工资 -> grade_salary_2025
2025-07-16 21:34:02.740 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [6071.0, 5441.0, 5081.0, 4931.0, 4781.0, 4631.0, 4631.0, 4631.0, 4631.0, 4481.0]
2025-07-16 21:34:02.740 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-16 21:34:02.740 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3389 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-16 21:34:02.740 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时32.8ms
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6018 | 🔧 [新架构排序] 表头点击: 列6
2025-07-16 21:34:02.740 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3036 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6024 | 🔧 [排序循环] 列6: ascending → descending
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3061 | 数据内容: 50行 x 28列
2025-07-16 21:34:02.740 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_column_sort_state:6108 | 🔧 [排序循环] 设置列6排序: descending
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.prototype_main_window:set_data:663 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-16 21:34:02.740 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-16 21:34:02.740 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3555 | 🆕 [新架构排序] 处理排序应用: 列6, 2025年薪级工资, descending
2025-07-16 21:34:02.740 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-16 21:34:02.754 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3569 | 🔧 [排序修复] 当前页码: 1
2025-07-16 21:34:02.754 | WARNING  | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1168 | 架构工厂不可用，跳过字段映射
2025-07-16 21:34:02.754 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-16 21:34:02.754 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3349 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-07-16 21:34:02.754 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-16 21:34:02.754 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3370 | 🔧 [排序] 字段转换: 2025年薪级工资 -> grade_salary_2025
2025-07-16 21:34:02.754 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1219 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-16 21:34:02.754 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-16 21:34:02.754 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-16 21:34:02.754 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-16 21:34:02.754 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1262 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-16 21:34:02.800 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3585 | 最大可见行数已更新: 50 -> 50
2025-07-16 21:34:02.800 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3638 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-16 21:34:02.800 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时60.8ms
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19850005.0
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 19860112.0
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 19870288.0
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 19870673.0
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 19870883.0
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19850005.0', '19860112.0', '19870288.0', '19870673.0', '19870883.0']
2025-07-16 21:34:02.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2161 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 156.21ms
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 初始化和状态: 30.88ms (19.8%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 表头设置: 47.01ms (30.1%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 填充可见数据: 78.32ms (50.1%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2169 | 🔧 [性能分析] 最耗时步骤: 填充可见数据 (78.32ms)
2025-07-16 21:34:02.926 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-16 21:34:02.926 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3087 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3098 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3104 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3389 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-16 21:34:02.926 | INFO     | src.services.table_data_service:_handle_sort_request:135 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-16 21:34:02.926 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3036 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-16 21:34:02.942 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3061 | 数据内容: 50行 x 28列
2025-07-16 21:34:02.942 | INFO     | src.core.unified_data_request_manager:request_table_data:177 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-16 21:34:02.942 | INFO     | src.gui.prototype.prototype_main_window:set_data:663 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-16 21:34:02.942 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-16 21:34:02.942 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-16 21:34:02.942 | WARNING  | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1168 | 架构工厂不可用，跳过字段映射
2025-07-16 21:34:02.942 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-16 21:34:02.957 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:634 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-16 21:34:02.957 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1219 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-16 21:34:02.957 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-16 21:34:02.957 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-16 21:34:02.957 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-16 21:34:02.957 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [排序调试] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 0
2025-07-16 21:34:02.957 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1262 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-16 21:34:02.972 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:559 | 🔧 [排序调试] 查询结果中 grade_salary_2025 的前10个值: [6071.0, 5441.0, 5081.0, 4931.0, 4781.0, 4631.0, 4631.0, 4631.0, 4631.0, 4481.0]
2025-07-16 21:34:03.004 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:561 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-16 21:34:03.004 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3585 | 最大可见行数已更新: 50 -> 50
2025-07-16 21:34:03.004 | INFO     | src.core.unified_data_request_manager:request_table_data:226 | 数据请求处理完成: 28字段, 50行, 耗时62.2ms
2025-07-16 21:34:03.004 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3638 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2262 | 🔧 [修复标识] 字段映射生效，样例: ['employee_id', 'employee_name', 'department'] -> ['工号', '姓名', '部门名称']
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第0行数据工号: 19961347.0
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第1行数据工号: 20251003.0
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第2行数据工号: 20251006.0
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第3行数据工号: 20251007.0
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:419 | 🔧 [排序修复] 第4行数据工号: 20251008.0
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:457 | 🔧 [排序修复] 可见行数据顺序: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 50 行, 24 列
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2388 | 🔧 [表格调试] 开始格式化ID字段: 列=工号, 原值=19961347.0, 类型=<class 'str'>
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2395 | 🔧 [表格调试] ID字段格式化完成: 列=工号, 原值=19961347.0, 格式化后=19961347.0
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2388 | 🔧 [表格调试] 开始格式化ID字段: 列=人员类别代码, 原值=17.0, 类型=<class 'str'>
2025-07-16 21:34:03.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2395 | 🔧 [表格调试] ID字段格式化完成: 列=人员类别代码, 原值=17.0, 格式化后=17.0
2025-07-16 21:34:03.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2161 | 🔧 [性能分析] 表格数据设置完成: 50 行, 最大可见行数: 50, 总耗时: 94.10ms
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 初始化和状态: 47.03ms (50.0%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 字段映射: 15.68ms (16.7%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 数据模型更新: 0.00ms (0.0%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 表头设置: 0.00ms (0.0%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 填充可见数据: 31.40ms (33.4%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2169 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (47.03ms)
2025-07-16 21:34:03.066 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-16 21:34:03.066 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3087 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3098 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-16 21:34:03.066 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3104 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-16 21:34:03.082 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3036 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-16 21:34:03.082 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3061 | 数据内容: 50行 x 28列
2025-07-16 21:34:03.082 | INFO     | src.gui.prototype.prototype_main_window:set_data:663 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-16 21:34:03.082 | WARNING  | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1168 | 架构工厂不可用，跳过字段映射
2025-07-16 21:34:03.082 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1219 | 🔧 [修复] 过滤了 4 个系统字段
2025-07-16 21:34:03.097 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:1262 | 🔧 [修复] 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段

2025-07-16 19:40:57.475 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-16 19:40:57.475 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-16 19:40:57.475 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-16 19:40:57.475 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-16 19:40:57.475 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-07-16 19:40:57.475 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-16 19:40:59.862 | INFO     | __main__:setup_app_logging:229 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-16 19:40:59.862 | INFO     | __main__:main:293 | 初始化核心管理器...
2025-07-16 19:40:59.862 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 19:40:59.862 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 19:40:59.862 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-16 19:40:59.862 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-07-16 19:40:59.862 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-16 19:40:59.894 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-16 19:40:59.894 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-16 19:40:59.894 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 19:40:59.894 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-16 19:40:59.894 | INFO     | __main__:main:298 | 核心管理器初始化完成。
2025-07-16 19:40:59.894 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-16 19:40:59.894 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-16 19:40:59.956 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-07-16 19:40:59.972 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-07-16 19:41:00.079 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:130 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-16 19:41:00.079 | INFO     | src.modules.data_import.config_sync_manager:__init__:71 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-07-16 19:41:00.079 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-16 19:41:00.079 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-16 19:41:00.079 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-16 19:41:00.079 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-16 19:41:00.079 | INFO     | src.core.unified_data_request_manager:__init__:164 | 统一数据请求管理器初始化完成
2025-07-16 19:41:00.079 | INFO     | src.core.unified_state_manager:_load_state:435 | 状态文件不存在，使用默认状态
2025-07-16 19:41:00.079 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-16 19:41:00.079 | INFO     | src.services.table_data_service:__init__:68 | 表格数据服务初始化完成
2025-07-16 19:41:00.079 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 107.2ms
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.prototype_main_window:__init__:2879 | 🚀 性能管理器已集成
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.prototype_main_window:__init__:2881 | ✅ 新架构集成成功！
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:2992 | 🔧 [修复标识] ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-16 19:41:00.111 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:2958 | ✅ 新架构事件监听器设置完成
2025-07-16 19:41:00.111 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-16 19:41:00.126 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-07-16 19:41:00.126 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-16 19:41:00.361 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2161 | 菜单栏创建完成
2025-07-16 19:41:00.361 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-16 19:41:00.361 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-16 19:41:00.361 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-16 19:41:00.376 | INFO     | src.gui.prototype.prototype_main_window:__init__:2137 | 菜单栏管理器初始化完成
2025-07-16 19:41:00.376 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-07-16 19:41:00.376 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:3807 | 管理器设置完成，包含增强版表头管理器
2025-07-16 19:41:00.376 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-16 19:41:00.376 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-16 19:41:00.376 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-07-16 19:41:00.376 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-16 19:41:00.394 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-16 19:41:00.409 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-07-16 19:41:00.410 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-16 19:41:00.410 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-07-16 19:41:00.438 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1312 | 快捷键注册完成: 18/18 个
2025-07-16 19:41:00.438 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1523 | 拖拽排序管理器初始化完成
2025-07-16 19:41:00.442 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-07-16 19:41:00.458 | INFO     | src.gui.multi_column_sort_manager:__init__:103 | 多列排序管理器初始化完成，最大排序列数: 3
2025-07-16 19:41:00.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1917 | 多列排序管理器初始化完成
2025-07-16 19:41:00.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1934 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-16 19:41:00.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3614 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-16 19:41:00.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-16 19:41:00.475 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 5.19ms
2025-07-16 19:41:00.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 初始化和状态: 1.04ms (19.9%)
2025-07-16 19:41:00.477 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 19:41:00.478 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 数据模型更新: 1.06ms (20.4%)
2025-07-16 19:41:00.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 表头设置: 1.03ms (19.9%)
2025-07-16 19:41:00.490 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 19:41:00.491 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-16 19:41:00.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 19:41:00.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 设置行号: 2.07ms (39.8%)
2025-07-16 19:41:00.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 最耗时步骤: 设置行号 (2.07ms)
2025-07-16 19:41:00.495 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_fix_header_signal_connections:2220 | 🔧 [关键修复] 表头信号连接修复失败: 'VirtualizedExpandableTable' object has no attribute '_on_header_sort_changed'
2025-07-16 19:41:00.503 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1736 | 表格已初始化为空白状态，等待用户导入数据
2025-07-16 19:41:00.507 | INFO     | src.gui.widgets.pagination_widget:__init__:165 | 分页组件初始化完成
2025-07-16 19:41:00.534 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:533 | 控制面板按钮信号连接完成
2025-07-16 19:41:00.547 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-07-16 19:41:00.553 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-16 19:41:00.553 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:3780 | 快捷键设置完成
2025-07-16 19:41:00.553 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:3769 | 主窗口UI设置完成。
2025-07-16 19:41:00.553 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3818 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-16 19:41:00.553 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3846 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-07-16 19:41:00.553 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3855 | ✅ 已连接分页组件事件到新架构
2025-07-16 19:41:00.561 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:3857 | 信号连接设置完成
2025-07-16 19:41:00.562 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:4574 | 已加载字段映射信息，共0个表的映射
2025-07-16 19:41:00.572 | WARNING  | src.gui.prototype.prototype_main_window:set_data:623 | 尝试设置空数据，将显示提示信息。
2025-07-16 19:41:00.575 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3614 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-16 19:41:00.575 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-16 19:41:00.578 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 3.75ms
2025-07-16 19:41:00.579 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 初始化和状态: 0.59ms (15.6%)
2025-07-16 19:41:00.580 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 19:41:00.581 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 数据模型更新: 1.06ms (28.3%)
2025-07-16 19:41:00.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 表头设置: 2.10ms (56.0%)
2025-07-16 19:41:00.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 19:41:00.591 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-16 19:41:00.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 19:41:00.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-16 19:41:00.593 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 最耗时步骤: 表头设置 (2.10ms)
2025-07-16 19:41:00.594 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_fix_header_signal_connections:2220 | 🔧 [关键修复] 表头信号连接修复失败: 'VirtualizedExpandableTable' object has no attribute '_on_header_sort_changed'
2025-07-16 19:41:00.595 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1736 | 表格已初始化为空白状态，等待用户导入数据
2025-07-16 19:41:00.596 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-16 19:41:00.596 | WARNING  | src.gui.prototype.prototype_main_window:set_data:623 | 尝试设置空数据，将显示提示信息。
2025-07-16 19:41:00.597 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3614 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-16 19:41:00.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:424 | 表格数据已设置: 0 行, 7 列
2025-07-16 19:41:00.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2164 | 🔧 [性能分析] 表格数据设置完成: 0 行, 最大可见行数: 1000, 总耗时: 10.40ms
2025-07-16 19:41:00.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 初始化和状态: 7.69ms (74.0%)
2025-07-16 19:41:00.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 字段映射: 0.00ms (0.0%)
2025-07-16 19:41:00.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 数据模型更新: 1.07ms (10.3%)
2025-07-16 19:41:00.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 表头设置: 1.63ms (15.7%)
2025-07-16 19:41:00.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 设置行数: 0.00ms (0.0%)
2025-07-16 19:41:00.620 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 填充可见数据: 0.00ms (0.0%)
2025-07-16 19:41:00.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 调整列宽: 0.00ms (0.0%)
2025-07-16 19:41:00.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2167 | 🔧 [性能分析] - 设置行号: 0.00ms (0.0%)
2025-07-16 19:41:00.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2172 | 🔧 [性能分析] 最耗时步骤: 初始化和状态 (7.69ms)
2025-07-16 19:41:00.623 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_fix_header_signal_connections:2220 | 🔧 [关键修复] 表头信号连接修复失败: 'VirtualizedExpandableTable' object has no attribute '_on_header_sort_changed'
2025-07-16 19:41:00.626 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1736 | 表格已初始化为空白状态，等待用户导入数据
2025-07-16 19:41:00.626 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-16 19:41:00.628 | INFO     | src.gui.prototype.prototype_main_window:_setup_legacy_compatibility:6447 | ✅ 老架构兼容性接口设置完成
2025-07-16 19:41:00.633 | INFO     | src.gui.prototype.prototype_main_window:__init__:2932 | 原型主窗口初始化完成
2025-07-16 19:41:00.894 | INFO     | __main__:main:320 | 应用程序启动成功
2025-07-16 19:41:00.902 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-07-16 19:41:00.903 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1884 | MainWorkspaceArea 响应式适配: sm
2025-07-16 19:41:00.928 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-07-16 19:41:00.929 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-07-16 19:41:01.192 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-07-16 19:41:01.192 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-16 19:41:01.192 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-16 19:41:01.192 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-16 19:41:01.192 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:968 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-16 19:41:01.192 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:880 | 找到 3 个总表
2025-07-16 19:41:01.208 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1290 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-16 19:41:01.208 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1187 | 使用兜底数据加载导航
2025-07-16 19:41:01.409 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-07-16 19:41:01.409 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1028 | 开始获取最新工资数据路径...
2025-07-16 19:41:01.409 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-16 19:41:01.409 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1033 | 未找到任何工资数据表
2025-07-16 19:41:01.409 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1098 | 未找到最新工资数据路径
2025-07-16 19:41:07.128 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:550 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-16 19:41:07.128 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4089 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-16 19:41:07.128 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-16 19:41:07.144 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-07-16 19:41:07.144 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-16 19:41:07.144 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-16 19:41:07.144 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:877 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-16 19:41:07.178 | ERROR    | src.gui.main_dialogs:_get_template_fields:1908 | 获取模板字段失败: 'NoneType' object has no attribute '_load_config_file'
2025-07-16 19:41:07.178 | WARNING  | src.gui.main_dialogs:_init_field_mapping:1859 | 未找到字段模板，使用默认字段列表
2025-07-16 19:41:07.206 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-16 19:41:07.221 | INFO     | src.gui.main_dialogs:_apply_default_settings:2206 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-16 19:41:07.359 | INFO     | src.gui.main_dialogs:_setup_tooltips:2461 | 工具提示设置完成
2025-07-16 19:41:07.384 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2500 | 快捷键设置完成
2025-07-16 19:41:07.384 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-16 19:41:15.149 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 19:41:16.550 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 19:41:16.552 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-16 19:41:16.553 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2241 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-16 19:41:29.404 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 19:41:29.591 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 19:41:29.607 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 19:41:29.607 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:202 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 19:41:29.607 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 19:41:29.810 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 19:41:29.810 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:213 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-16 19:41:29.825 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 19:41:29.825 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 19:41:29.825 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 19:41:29.919 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-16 19:41:29.935 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-16 19:41:29.935 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:447 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:728 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-16 19:41:29.935 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:464 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-16 19:41:29.935 | ERROR    | src.modules.data_import.multi_sheet_importer:_process_sheet_data:529 | 处理Sheet 离休人员工资表 数据失败: 'NoneType' object has no attribute 'load_mapping'
2025-07-16 19:41:29.950 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 19:41:29.966 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 19:41:29.966 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 19:41:30.060 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-16 19:41:30.076 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-16 19:41:30.076 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:447 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-07-16 19:41:30.076 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:728 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-16 19:41:30.091 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-16 19:41:30.091 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:464 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-16 19:41:30.091 | ERROR    | src.modules.data_import.multi_sheet_importer:_process_sheet_data:529 | 处理Sheet 退休人员工资表 数据失败: 'NoneType' object has no attribute 'load_mapping'
2025-07-16 19:41:30.091 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 19:41:30.091 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 19:41:30.091 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 19:41:30.232 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-16 19:41:30.232 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 19:41:30.232 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-16 19:41:30.232 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-16 19:41:30.232 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 19:41:30.232 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-07-16 19:41:30.232 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-16 19:41:30.247 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-16 19:41:30.247 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:447 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-07-16 19:41:30.247 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:728 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-16 19:41:30.247 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-16 19:41:30.247 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:464 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-16 19:41:30.247 | ERROR    | src.modules.data_import.multi_sheet_importer:_process_sheet_data:529 | 处理Sheet 全部在职人员工资表 数据失败: 'NoneType' object has no attribute 'load_mapping'
2025-07-16 19:41:30.247 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-16 19:41:30.247 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-16 19:41:30.247 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-16 19:41:30.372 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-16 19:41:30.372 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-16 19:41:30.372 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-16 19:41:30.372 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-16 19:41:30.372 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据导入过滤: 发现1条姓名为空的记录
2025-07-16 19:41:30.372 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:569 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-07-16 19:41:30.388 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:607 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-16 19:41:30.388 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-16 19:41:30.388 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:447 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-07-16 19:41:30.388 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:728 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-16 19:41:30.388 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-16 19:41:30.388 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:464 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-16 19:41:30.388 | ERROR    | src.modules.data_import.multi_sheet_importer:_process_sheet_data:529 | 处理Sheet A岗职工 数据失败: 'NoneType' object has no attribute 'load_mapping'
2025-07-16 19:41:30.388 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:232 | 多Sheet导入完成: {'success': False, 'results': {}, 'total_records': 0, 'import_stats': {'total_sheets': 4, 'processed_sheets': 0, 'total_records': 0, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-16 19:41:51.916 | INFO     | __main__:main:325 | 应用程序正常退出

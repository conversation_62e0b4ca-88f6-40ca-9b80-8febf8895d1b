"""
表头管理器 - 增强版表头重影修复

功能说明:
- 强化表头缓存清理机制
- 防止中英文表头重影问题
- 提供表头状态检测和验证
- 统一管理所有表格组件的表头
"""

import time
from typing import Dict, List, Optional, Set, Any
from PyQt5.QtWidgets import QTableWidget, QHeaderView, QWidget, QApplication, QTableWidgetItem
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont

from src.utils.log_config import setup_logger


class HeaderState:
    """表头状态信息"""
    
    def __init__(self, table_id: str):
        self.table_id = table_id
        self.last_update_time = time.time()
        self.header_labels = []
        self.is_updating = False
        self.update_count = 0
        self.has_shadow_issue = False
        

class TableHeaderManager(QObject):
    """
    表头管理器 - 增强版
    
    负责管理所有表格的表头，防止重影问题，提供以下功能：
    1. 强化表头缓存清理
    2. 表头状态检测和验证
    3. 防重复更新机制
    4. 延迟清理策略
    5. 表头重影检测和修复
    """
    
    # 信号定义
    header_cleaned = pyqtSignal(str)  # 表头清理完成信号 (table_id)
    shadow_detected = pyqtSignal(str, list)  # 检测到重影信号 (table_id, duplicate_labels)
    header_updated = pyqtSignal(str, list)  # 表头更新信号 (table_id, new_labels)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 表头状态管理
        self.header_states: Dict[str, HeaderState] = {}
        self.registered_tables: Dict[str, QTableWidget] = {}
        self.cleaning_in_progress = False
        
        # 配置参数
        self.max_update_frequency = 0.1  # 最大更新频率（秒）
        self.cleanup_delay = 50  # 延迟清理时间（毫秒）
        self.shadow_detection_enabled = True
        
        # 统计信息
        self.total_cleanups = 0
        self.shadow_fixes = 0
        self.last_cleanup_time = 0
        
        self.logger.info("表头管理器初始化完成")
    
    def register_table(self, table_id: str, table: QTableWidget) -> bool:
        """
        注册表格组件
        
        Args:
            table_id: 表格唯一标识
            table: 表格组件
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if table_id in self.registered_tables:
                self.logger.warning(f"表格 {table_id} 已经注册，将覆盖现有注册")
            
            self.registered_tables[table_id] = table
            self.header_states[table_id] = HeaderState(table_id)
            
            # 暂时不连接自动信号，避免过度触发
            # if hasattr(table, 'itemChanged'):
            #     table.itemChanged.connect(lambda: self._on_table_changed(table_id))
            
            self.logger.debug(f"表格 {table_id} 注册成功")
            return True
            
        except Exception as e:
            self.logger.error(f"注册表格 {table_id} 失败: {e}")
            return False
    
    def unregister_table(self, table_id: str) -> bool:
        """
        注销表格组件
        
        Args:
            table_id: 表格唯一标识
            
        Returns:
            bool: 注销是否成功
        """
        try:
            if table_id in self.registered_tables:
                del self.registered_tables[table_id]
            
            if table_id in self.header_states:
                del self.header_states[table_id]
            
            self.logger.debug(f"表格 {table_id} 注销成功")
            return True
            
        except Exception as e:
            self.logger.error(f"注销表格 {table_id} 失败: {e}")
            return False
    
    def clear_table_header_cache_enhanced(self, table_id: Optional[str] = None) -> bool:
        """
        增强版表头缓存清理（防重复版本）
        
        Args:
            table_id: 指定表格ID，如果为None则清理所有注册的表格
            
        Returns:
            bool: 清理是否成功
        """
        current_time = time.time()
        
        # 【关键修复】优化频率控制：检查强制清理标志
        force_clean = getattr(self, '_force_next_clean', False)
        
        # 防止过于频繁的清理，但允许强制清理
        # 🔧 [修复标识] 降低频率限制，允许更频繁的清理操作
        if not force_clean and current_time - self.last_cleanup_time < 0.5:
            self.logger.debug(f"清理过于频繁，跳过（距离上次清理 {current_time - self.last_cleanup_time:.1f}s）")
            return False
        
        if self.cleaning_in_progress:
            self.logger.debug("表头清理正在进行中，跳过重复清理")
            return False
        
        # 重置强制清理标志
        self._force_next_clean = False
        
        self.cleaning_in_progress = True
        
        try:
            # 更新统计信息
            self.total_cleanups += 1
            self.last_cleanup_time = time.time()
            
            # 确定要清理的表格
            tables_to_clean = []
            if table_id:
                if table_id in self.registered_tables:
                    tables_to_clean.append((table_id, self.registered_tables[table_id]))
            else:
                tables_to_clean = list(self.registered_tables.items())
            
            success_count = 0
            for tid, table in tables_to_clean:
                if self._clear_single_table_header(tid, table):
                    success_count += 1
            
            self.logger.info(f"表头缓存清理完成: {success_count}/{len(tables_to_clean)} 个表格成功清理")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"增强版表头清理失败: {e}")
            return False
        finally:
            self.cleaning_in_progress = False
    
    def _clear_single_table_header(self, table_id: str, table: QTableWidget) -> bool:
        """
        清理单个表格的表头

        Args:
            table_id: 表格标识
            table: 表格组件

        Returns:
            bool: 清理是否成功
        """
        try:
            # 🔧 [排序修复] 检查是否在排序过程中
            if hasattr(self, '_sort_in_progress') and self._sort_in_progress:
                self.logger.debug(f"表格 {table_id} 正在排序中，跳过表头清理")
                return False

            state = self.header_states.get(table_id)
            if state and state.is_updating:
                self.logger.debug(f"表格 {table_id} 正在更新中，跳过清理")
                return False
            
            # 1. 检测并记录当前表头状态
            current_labels = self._get_current_header_labels(table)
            shadow_detected = self._detect_header_shadows(current_labels)
            
            if shadow_detected:
                self.shadow_fixes += 1
                self.logger.warning(f"检测到表格 {table_id} 存在表头重影: {shadow_detected}")
                self.shadow_detected.emit(table_id, shadow_detected)
            
            # 2. 清除表头缓存
            h_header = table.horizontalHeader()
            v_header = table.verticalHeader()
            
            if h_header:
                # 强制重绘表头
                h_header.updateGeometry()
                h_header.update()
                h_header.repaint()
                
                # 清除选择状态避免重影
                if hasattr(h_header, 'clearSelection'):
                    h_header.clearSelection()
                
                # 重置表头样式（防止样式缓存问题）
                self._reset_header_style(h_header)
            
            if v_header:
                v_header.updateGeometry()
                v_header.update()
                v_header.repaint()
            
            # 3. 清除表格视口缓存
            viewport = table.viewport()
            if viewport:
                viewport.update()
                viewport.repaint()
            
            # 4. 强制刷新整个表格
            table.updateGeometry()
            table.update()
            table.repaint()
            
            # 5. 更新状态信息
            if state:
                state.last_update_time = time.time()
                state.update_count += 1
                state.header_labels = current_labels
                state.has_shadow_issue = bool(shadow_detected)
            
            self.logger.debug(f"表格 {table_id} 表头清理完成")
            self.header_cleaned.emit(table_id)
            return True
            
        except Exception as e:
            self.logger.error(f"清理表格 {table_id} 表头失败: {e}")
            return False
    
    def _get_current_header_labels(self, table: QTableWidget) -> List[str]:
        """
        获取当前表头标签
        
        Args:
            table: 表格组件
            
        Returns:
            List[str]: 表头标签列表
        """
        labels = []
        try:
            for i in range(table.columnCount()):
                header_item = table.horizontalHeaderItem(i)
                if header_item:
                    labels.append(header_item.text())
                else:
                    labels.append(f"Column_{i}")
        except Exception as e:
            self.logger.error(f"获取表头标签失败: {e}")
        
        return labels
    
    def _detect_header_shadows(self, labels: List[str]) -> List[str]:
        """
        检测表头重影（重复标签）
        
        Args:
            labels: 表头标签列表
            
        Returns:
            List[str]: 重复的标签列表
        """
        if not self.shadow_detection_enabled or not labels:
            return []
        
        seen = set()
        duplicates = set()
        
        for label in labels:
            if label in seen:
                duplicates.add(label)
            else:
                seen.add(label)
        
        # 检测中英文混合的情况
        chinese_english_pairs = self._detect_chinese_english_duplicates(labels)
        duplicates.update(chinese_english_pairs)
        
        return list(duplicates)
    
    def _detect_chinese_english_duplicates(self, labels: List[str]) -> Set[str]:
        """
        检测中英文对应的重复标签
        
        Args:
            labels: 表头标签列表
            
        Returns:
            Set[str]: 中英文重复对应的标签
        """
        duplicates = set()
        
        # 定义中英文对应关系
        chinese_english_map = {
            '工号': ['employee_id', 'emp_id', 'id'],
            '姓名': ['name', 'employee_name', 'emp_name'],
            '身份证号': ['id_card', 'identity_card'],
            '部门': ['department', 'dept'],
            '职位': ['position', 'title', 'job_title'],
            '基本工资': ['basic_salary', 'base_salary'],
            '职务工资': ['position_salary', 'position_salary_2025'],
            '薪级工资': ['grade_salary', 'grade_salary_2025'],
            '2025年薪级工资': ['grade_salary_2025'],
            '2025年职务工资': ['position_salary_2025'],
            '津贴补贴': ['allowance', 'subsidy'],
            '应发合计': ['gross_pay', 'total_gross'],
            '实发合计': ['net_pay', 'total_net'],
            '扣款合计': ['deduction', 'total_deduction'],
            '住房公积金': ['housing_fund', 'provident_fund'],
            '医疗保险': ['medical_insurance', 'health_insurance'],
            '养老保险': ['pension_insurance', 'retirement_insurance'],
            '失业保险': ['unemployment_insurance'],
            '工伤保险': ['work_injury_insurance'],
            '生育保险': ['maternity_insurance'],
        }
        
        labels_set = set(labels)
        
        for chinese, english_list in chinese_english_map.items():
            if chinese in labels_set:
                for english in english_list:
                    if english in labels_set:
                        duplicates.add(chinese)
                        duplicates.add(english)
                        self.logger.debug(f"检测到中英文重复: {chinese} <-> {english}")
        
        return duplicates
    
    def _reset_header_style(self, header: QHeaderView):
        """
        重置表头样式，清除可能的样式缓存
        
        Args:
            header: 表头组件
        """
        try:
            # 重置字体
            font = header.font()
            header.setFont(QFont())
            header.setFont(font)
            
            # 重置样式表（如果有的话）
            current_style = header.styleSheet()
            if current_style:
                header.setStyleSheet("")
                header.setStyleSheet(current_style)
            
        except Exception as e:
            self.logger.error(f"重置表头样式失败: {e}")
    
    def validate_header_state(self, table_id: str) -> Dict[str, Any]:
        """
        验证表头状态，检测潜在问题
        
        Args:
            table_id: 表格标识
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'table_id': table_id,
            'is_valid': True,
            'issues': [],
            'recommendations': []
        }
        
        try:
            if table_id not in self.registered_tables:
                result['is_valid'] = False
                result['issues'].append('表格未注册')
                return result
            
            table = self.registered_tables[table_id]
            state = self.header_states.get(table_id)
            
            # 检查表头数量
            column_count = table.columnCount()
            if column_count == 0:
                result['issues'].append('表格没有列')
            
            # 检查表头标签
            labels = self._get_current_header_labels(table)
            duplicates = self._detect_header_shadows(labels)
            
            if duplicates:
                result['is_valid'] = False
                result['issues'].append(f'检测到重复表头: {duplicates}')
                result['recommendations'].append('建议执行表头清理')
            
            # 检查更新频率
            if state and state.update_count > 10:
                current_time = time.time()
                if current_time - state.last_update_time < self.max_update_frequency * 5:
                    result['issues'].append('表头更新过于频繁')
                    result['recommendations'].append('建议检查更新逻辑')
            
            self.logger.debug(f"表格 {table_id} 状态验证完成: {result}")
            
        except Exception as e:
            result['is_valid'] = False
            result['issues'].append(f'验证过程出错: {e}')
            self.logger.error(f"验证表格 {table_id} 状态失败: {e}")
        
        return result
    
    def delayed_cleanup(self, table_id: Optional[str] = None, delay_ms: int = None) -> bool:
        """
        延迟执行表头清理
        
        Args:
            table_id: 表格标识，None表示清理所有
            delay_ms: 延迟时间（毫秒），None使用默认值
            
        Returns:
            bool: 延迟清理是否启动成功
        """
        try:
            actual_delay = delay_ms if delay_ms is not None else self.cleanup_delay
            
            QTimer.singleShot(actual_delay, lambda: self.clear_table_header_cache_enhanced(table_id))
            
            self.logger.debug(f"延迟表头清理已启动: table_id={table_id}, delay={actual_delay}ms")
            return True
            
        except Exception as e:
            self.logger.error(f"启动延迟清理失败: {e}")
            return False
    
    def auto_detect_and_fix_shadows(self) -> Dict[str, List[str]]:
        """
        自动检测并修复所有表格的表头重影
        
        Returns:
            Dict[str, List[str]]: 检测到重影的表格及其重复标签
        """
        shadow_tables = {}
        
        try:
            for table_id, table in self.registered_tables.items():
                labels = self._get_current_header_labels(table)
                duplicates = self._detect_header_shadows(labels)
                
                if duplicates:
                    shadow_tables[table_id] = duplicates
                    self.logger.warning(f"自动修复表格 {table_id} 的表头重影: {duplicates}")
                    
                    # 立即执行清理
                    self._clear_single_table_header(table_id, table)
            
            if shadow_tables:
                self.logger.info(f"自动修复完成，处理了 {len(shadow_tables)} 个表格的重影问题")
            else:
                self.logger.debug("未检测到表头重影问题")
                
        except Exception as e:
            self.logger.error(f"自动检测修复失败: {e}")
        
        return shadow_tables

    def enhanced_auto_detect_and_fix_shadows(self) -> Dict[str, Any]:
        """
        【第三层防护】增强版自动检测并修复表头重影

        相比普通版本，增加了以下功能：
        1. 更详细的检测报告
        2. 预防性清理
        3. 修复效果验证
        4. 性能统计

        Returns:
            Dict[str, Any]: 详细的检测和修复报告
        """
        start_time = time.time()
        report = {
            'total_tables': len(self.registered_tables),
            'shadow_tables': {},
            'fixed_tables': [],
            'failed_tables': [],
            'performance': {},
            'recommendations': []
        }

        try:
            self.logger.info(f"开始增强版表头重影检测，共 {len(self.registered_tables)} 个表格")

            for table_id, table in self.registered_tables.items():
                table_start_time = time.time()

                try:
                    # 1. 获取当前表头状态
                    labels = self._get_current_header_labels(table)
                    duplicates = self._detect_header_shadows(labels)

                    if duplicates:
                        report['shadow_tables'][table_id] = {
                            'duplicates': duplicates,
                            'total_headers': len(labels),
                            'duplicate_count': len(duplicates)
                        }

                        self.logger.warning(f"检测到表格 {table_id} 的表头重影: {duplicates}")

                        # 2. 执行修复
                        fix_success = self._enhanced_fix_table_header(table_id, table, duplicates)

                        if fix_success:
                            # 3. 验证修复效果
                            new_labels = self._get_current_header_labels(table)
                            new_duplicates = self._detect_header_shadows(new_labels)

                            if not new_duplicates:
                                report['fixed_tables'].append(table_id)
                                self.logger.info(f"表格 {table_id} 表头重影修复成功")
                            else:
                                report['failed_tables'].append({
                                    'table_id': table_id,
                                    'reason': f"修复后仍有重影: {new_duplicates}"
                                })
                                self.logger.error(f"表格 {table_id} 表头重影修复失败，仍有重影: {new_duplicates}")
                        else:
                            report['failed_tables'].append({
                                'table_id': table_id,
                                'reason': "修复过程失败"
                            })
                    else:
                        # 即使没有重影，也进行预防性清理
                        self._preventive_header_cleanup(table_id, table)

                    # 记录单表处理时间
                    table_time = (time.time() - table_start_time) * 1000
                    report['performance'][table_id] = f"{table_time:.2f}ms"

                except Exception as table_error:
                    self.logger.error(f"处理表格 {table_id} 时发生错误: {table_error}")
                    report['failed_tables'].append({
                        'table_id': table_id,
                        'reason': str(table_error)
                    })

            # 生成建议
            self._generate_recommendations(report)

            total_time = (time.time() - start_time) * 1000
            report['performance']['total_time'] = f"{total_time:.2f}ms"

            self.logger.info(f"增强版表头重影检测完成，耗时 {total_time:.2f}ms")
            self.logger.info(f"检测结果：{len(report['shadow_tables'])} 个重影表格，{len(report['fixed_tables'])} 个修复成功，{len(report['failed_tables'])} 个修复失败")

        except Exception as e:
            self.logger.error(f"增强版表头重影检测失败: {e}")
            report['error'] = str(e)

        return report

    def _enhanced_fix_table_header(self, table_id: str, table: QTableWidget, duplicates: List[str]) -> bool:
        """
        增强版表头修复方法

        Args:
            table_id: 表格ID
            table: 表格组件
            duplicates: 重复的表头标签

        Returns:
            bool: 修复是否成功
        """
        try:
            self.logger.debug(f"开始增强修复表格 {table_id} 的表头重影")

            # 1. 保存当前状态
            original_labels = self._get_current_header_labels(table)

            # 2. 执行深度清理
            success = self._deep_header_cleanup(table_id, table)
            if not success:
                self.logger.error(f"表格 {table_id} 深度清理失败")
                return False

            # 3. 等待一小段时间确保清理完成
            QApplication.processEvents()
            time.sleep(0.01)  # 10ms

            # 4. 重新设置表头（如果有原始数据）
            if hasattr(table, 'original_headers') and table.original_headers:
                self._safe_restore_headers(table, table.original_headers)

            self.logger.debug(f"表格 {table_id} 增强修复完成")
            return True

        except Exception as e:
            self.logger.error(f"增强修复表格 {table_id} 失败: {e}")
            return False

    def _deep_header_cleanup(self, table_id: str, table: QTableWidget) -> bool:
        """
        深度表头清理

        Args:
            table_id: 表格ID
            table: 表格组件

        Returns:
            bool: 清理是否成功
        """
        try:
            # 1. 基础清理
            basic_success = self._clear_single_table_header(table_id, table)

            # 2. 额外的深度清理步骤
            h_header = table.horizontalHeader()
            if h_header:
                # 强制重置表头状态
                h_header.reset()
                h_header.clearSelection()

                # 多次刷新确保清理彻底
                for _ in range(3):
                    h_header.updateGeometry()
                    h_header.update()
                    h_header.repaint()
                    QApplication.processEvents()

            # 3. 清理表格内部缓存
            table.clearContents()
            table.clearSelection()

            # 4. 强制刷新整个组件
            table.updateGeometry()
            table.update()
            table.repaint()

            return basic_success

        except Exception as e:
            self.logger.error(f"深度清理表格 {table_id} 失败: {e}")
            return False

    def _preventive_header_cleanup(self, table_id: str, table: QTableWidget):
        """
        预防性表头清理（即使没有检测到重影也进行轻度清理）

        Args:
            table_id: 表格ID
            table: 表格组件
        """
        try:
            h_header = table.horizontalHeader()
            if h_header:
                h_header.updateGeometry()
                h_header.update()

            viewport = table.viewport()
            if viewport:
                viewport.update()

        except Exception as e:
            self.logger.debug(f"预防性清理表格 {table_id} 失败: {e}")

    def _safe_restore_headers(self, table: QTableWidget, headers: List[str]):
        """
        安全地恢复表头

        Args:
            table: 表格组件
            headers: 表头列表
        """
        try:
            if table.columnCount() != len(headers):
                table.setColumnCount(len(headers))

            for i, header in enumerate(headers):
                if i < table.columnCount():
                    header_item = QTableWidgetItem(str(header))
                    table.setHorizontalHeaderItem(i, header_item)

        except Exception as e:
            self.logger.error(f"安全恢复表头失败: {e}")

    def _generate_recommendations(self, report: Dict[str, Any]):
        """
        根据检测结果生成建议

        Args:
            report: 检测报告
        """
        recommendations = []

        if len(report['shadow_tables']) > 0:
            recommendations.append("检测到表头重影问题，建议在数据设置前进行预清理")

        if len(report['failed_tables']) > 0:
            recommendations.append("部分表格修复失败，建议检查表格组件状态")

        if report['total_tables'] > 10:
            recommendations.append("表格数量较多，建议启用延迟清理机制")

        report['recommendations'] = recommendations

    def _on_table_changed(self, table_id: str):
        """
        表格内容变化回调
        
        Args:
            table_id: 表格标识
        """
        try:
            state = self.header_states.get(table_id)
            if state:
                current_time = time.time()
                
                # 防止过于频繁的更新
                if current_time - state.last_update_time < self.max_update_frequency:
                    return
                
                # 延迟清理表头
                self.delayed_cleanup(table_id, 100)
                
        except Exception as e:
            self.logger.error(f"处理表格 {table_id} 变化失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'registered_tables': len(self.registered_tables),
            'total_cleanups': self.total_cleanups,
            'shadow_fixes': self.shadow_fixes,
            'last_cleanup_time': self.last_cleanup_time,
            'cleaning_in_progress': self.cleaning_in_progress,
            'table_states': {tid: {
                'update_count': state.update_count,
                'has_shadow_issue': state.has_shadow_issue,
                'last_update': state.last_update_time
            } for tid, state in self.header_states.items()}
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_cleanups = 0
        self.shadow_fixes = 0
        self.last_cleanup_time = 0
        
        for state in self.header_states.values():
            state.update_count = 0
            state.has_shadow_issue = False
        
        self.logger.info("表头管理器统计信息已重置")
    
    def force_clear_next_time(self):
        """设置强制清理标志，确保下次清理不被频率限制"""
        self._force_next_clean = True
        self.logger.debug("已设置强制清理标志，下次清理将忽略频率限制")
    
    def clear_table_header_immediately(self, table_id: Optional[str] = None) -> bool:
        """立即执行表头清理，忽略频率限制"""
        self._force_next_clean = True
        return self.clear_table_header_cache_enhanced(table_id)


# 全局表头管理器实例（单例模式）
_global_header_manager = None

def get_global_header_manager() -> TableHeaderManager:
    """
    获取全局表头管理器实例
    
    Returns:
        TableHeaderManager: 全局管理器实例
    """
    global _global_header_manager
    if _global_header_manager is None:
        _global_header_manager = TableHeaderManager()
    return _global_header_manager 